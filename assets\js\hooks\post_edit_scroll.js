// Post edit scroll management hook for LiveView
const PostEditScroll = {
  // Store for scroll positions
  scrollPositions: new Map(),

  // Flag to indicate we're in a post-edit context
  isInEditContext: false,

  mounted() {
    // Listen for edit-post-scroll event from LiveView
    this.handleEvent("edit-post-scroll", ({ post_id }) => {
      this.saveScrollPosition();
      this.scrollToPost(post_id);
      // Set flag to indicate we're in edit context
      this.isInEditContext = true;
      // Make this flag globally accessible
      window.postEditScrollContext = this;
    });

    // Listen for restore-edit-scroll event from LiveView
    // This is used when a post is saved or closed to restore the original scroll position
    this.handleEvent("restore-edit-scroll", ({ post_id }) => {
      this.restoreScrollPosition(post_id);
      // Clear the edit context flag after a delay to allow for any subsequent operations
      setTimeout(() => {
        this.isInEditContext = false;
      }, 1000); // 1 second delay to allow for post collapse operations
    });
  },

  // Save the current scroll position
  saveScrollPosition() {
    const scrollContainer = document.querySelector('.hype-main-content');
    if (!scrollContainer) {
      console.error('Scrollable container not found');
      return;
    }

    const scrollPosition = scrollContainer.scrollTop;
    console.log('Saved scroll position for editing:', scrollPosition);

    // Store the scroll position
    this.scrollPositions.set('editScrollPosition', scrollPosition);
  },

  // Scroll to the post being edited
  scrollToPost(postId) {
    const post = document.getElementById(`post-${postId}`);
    if (!post) {
      console.error('Post not found:', postId);
      return;
    }

    const scrollContainer = document.querySelector('.hype-main-content');
    if (!scrollContainer) {
      console.error('Scrollable container not found');
      return;
    }

    // Calculate the position to scroll to
    const headerHeight = document.querySelector('.hype-navbar')?.offsetHeight || 0;
    const offset = headerHeight + 20;

    // Get the post's position relative to the container
    const postRect = post.getBoundingClientRect();
    const containerRect = scrollContainer.getBoundingClientRect();
    const relativeTop = postRect.top - containerRect.top + scrollContainer.scrollTop;

    // Scroll the container to show the post at the top (minus header offset)
    scrollContainer.scrollTo({
      top: relativeTop - offset,
      behavior: 'smooth'
    });

    // Add a backup scroll attempt for reliability
    setTimeout(() => {
      scrollContainer.scrollTo({
        top: relativeTop - offset,
        behavior: 'smooth'
      });
    }, 100);
  },

  // Restore the saved scroll position
  restoreScrollPosition(postId) {
    const scrollContainer = document.querySelector('.hype-main-content');
    if (!scrollContainer) {
      console.error('Scrollable container not found');
      return;
    }

    const scrollPosition = this.scrollPositions.get('editScrollPosition');
    if (scrollPosition === undefined) {
      console.warn('No saved scroll position found');
      return;
    }

    console.log('Restoring scroll position after editing:', scrollPosition);

    // Scroll the container to the saved position
    try {
      // Immediate scroll without smooth behavior for instant positioning
      scrollContainer.scrollTop = scrollPosition;

      // Multiple backup attempts with setTimeout for reliability
      // This helps ensure the scroll position is maintained even if other DOM updates occur
      setTimeout(() => {
        scrollContainer.scrollTop = scrollPosition;

        // Try again after a bit longer
        setTimeout(() => {
          scrollContainer.scrollTop = scrollPosition;
        }, 50);

        // And again after even longer
        setTimeout(() => {
          scrollContainer.scrollTop = scrollPosition;
        }, 150);
      }, 10);

      console.log('Restored container scroll position to:', scrollPosition);
    } catch (e) {
      console.error('Error restoring scroll position:', e);
    }
  }
};

export default PostEditScroll;
