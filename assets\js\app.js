// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import { Socket } from "phoenix"
import { LiveSocket } from "phoenix_live_view"
import topbar from "../vendor/topbar"
import setupPostExpansion from "./post_expansion"
import setupMobileMenu from "./mobile_menu"
import setupGridSizePersistence from "./grid_size"
import InfiniteScroll from "./hooks/infinite_scroll"
import Toast from "./hooks/toast"
import PostEditScroll from "./hooks/post_edit_scroll"

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")

// Define hooks for LiveView
const Hooks = {
  PostsContainer: {
    mounted() {
      setupPostExpansion();
      setupGridSizePersistence();

      // This handles direct navigation to a post URL
      // For direct URL access, the post is moved to the top of the list on the server side
      const handleDirectPostAccess = () => {
        // Get the current URL path
        const path = window.location.pathname;

        // Check if it's a post URL (e.g., /posts/123)
        const postMatch = path.match(/\/posts\/(\d+)/);

        if (postMatch && postMatch[1]) {
          const postId = parseInt(postMatch[1], 10);
          console.log('Direct post access detected in LiveView hook:', postId);

          // Set a flag to track this direct access
          window.directAccessPostId = postId;

          // Wait for the post to be available in the DOM
          const checkAndExpandPost = (retryCount = 0, maxRetries = 20) => {
            if (retryCount >= maxRetries) {
              console.error(`Failed to find post ${postId} after ${maxRetries} attempts`);
              return;
            }

            const postToExpand = document.getElementById(`post-${postId}`);

            if (postToExpand) {
              console.log(`Found post ${postId} in DOM after ${retryCount} attempts, handling expansion`);

              // Simply call handlePostExpansion
              // Our new implementation handles closing any existing modal
              setTimeout(() => {
                window.handlePostExpansion(postId);
              }, 100);
            } else {
              console.log(`Post ${postId} not found in DOM yet, retry ${retryCount + 1}/${maxRetries}...`);
              // Try again after a short delay
              setTimeout(() => {
                checkAndExpandPost(retryCount + 1, maxRetries);
              }, 100);
            }
          };

          // Start checking for the post
          setTimeout(() => {
            checkAndExpandPost();
          }, 100);
        }
      };

      // Start handling direct post access
      handleDirectPostAccess();

      // Listen for post expansion events from the server
      this.handleEvent("post-expanded", ({ id, from_click }) => {
        console.log(`Post expansion event received for post ${id}, from_click: ${from_click}`);

        // Update the URL to reflect the expanded post
        try {
          const currentPath = window.location.pathname;
          const expectedPath = `/posts/${id}`;

          if (currentPath !== expectedPath) {
            console.log(`Updating URL to ${expectedPath} from server event`);
            window.history.pushState({}, '', expectedPath);
          }
        } catch (e) {
          console.error('Error updating URL from server event:', e);
        }

        // Instead of calling handlePostExpansion (which would create a circular loop),
        // directly apply the visual expansion state since Phoenix already knows about it
        window.applyPostExpansionVisuals(id);
      });

      // Listen for post collapse events from the server
      this.handleEvent("post-collapsed", () => {
        console.log("Post collapse event received");

        // Update the URL to reflect the collapsed state
        try {
          const currentPath = window.location.pathname;
          if (currentPath.match(/\/posts\/\d+/)) {
            console.log('Updating URL to / from server collapse event');
            window.history.pushState({}, '', '/');
          }
        } catch (e) {
          console.error('Error updating URL from server collapse event:', e);
        }

        // Call the collapse handler
        window.handlePostCollapse();
      });
    },

    // Add handlers for URL changes
    updated() {
      // Check if the URL matches a post but no post is expanded
      const path = window.location.pathname;
      const postMatch = path.match(/\/posts\/(\d+)/);

      if (postMatch && postMatch[1]) {
        const postId = parseInt(postMatch[1], 10);
        const post = document.getElementById(`post-${postId}`);

        // If the post exists but is not expanded, expand it
        if (post && !post.classList.contains('expanded-post')) {
          console.log(`URL indicates post ${postId} should be expanded, but it's not. Expanding now.`);
          window.handlePostExpansion(postId);
        }
      }
    }
  },
  MobileMenu: {
    mounted() {
      console.log("MobileMenu hook mounted");
      setupMobileMenu();
    }
  },
  // Add the InfiniteScroll hook
  InfiniteScroll,
  // Add the Toast hook
  Toast,
  // Add the PostEditScroll hook
  PostEditScroll
};

let liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: { _csrf_token: csrfToken },
  hooks: Hooks
})

// Show progress bar on live navigation and form submits
topbar.config({ barColors: { 0: "#29d" }, shadowColor: "rgba(0, 0, 0, .3)" })
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

