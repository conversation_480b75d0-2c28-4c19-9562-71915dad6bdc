defmodule HypeArrowWeb.PostLive.Index do
  use Hype<PERSON><PERSON>Web, :live_view

  alias <PERSON>ype<PERSON>rrow.Social
  alias Hype<PERSON>rrow.Social.Post
  alias HypeArrowWeb.Utils.ErrorUtils
  alias HypeArrowWeb.Live.Hooks
  alias HypeArrowWeb.Live.Helpers
  alias HypeArrowWeb.PostLive.PostUtils

  import Hype<PERSON><PERSON>Web.LayoutComponents
  import HypeArrowWeb.PostComponents
  import HypeArrowWeb.Components.InfiniteScroll
  import HypeArrowWeb.ModalComponents
  import HypeArrowWeb.ContentGridComponents
  import HypeArrowWeb.Components.EditPostModal

  alias Phoenix.LiveView.JS

  @default_posts_per_page 10

  @impl true
  def mount(_params, session, socket) do
    # Initial load of posts with pagination
    {posts, page_info} = Social.paginate_posts(nil, @default_posts_per_page)

    # Create a changeset for the new post form
    changeset = Social.change_post(%Post{})

    socket =
      socket
      |> assign(:posts, posts)
      |> assign(:page_info, page_info)
      |> assign(:expanded_post_id, nil)
      |> assign(:loading, false)
      # Flag to track if expansion is from direct URL access
      |> assign(:from_direct_access, false)
      # Flag to track if expansion is for editing
      |> assign(:expanding_for_edit, false)
      # New post modal state
      |> assign(:show_new_post_modal, false)
      # New post form
      |> assign(:new_post_form, to_form(changeset))
      # Edit post modal state
      |> assign(:show_edit_post_modal, false)
      |> assign(:edit_post, nil)
      # Inline editing state
      |> assign(:editing_post_id, nil)
      |> assign(:edit_form, nil)
      # Track recently saved posts for scroll handling
      |> assign(:recently_saved_post_id, nil)
      # Initialize grid size from session or default
      |> Hooks.initialize_grid_size(session)
      # Subscribe to real-time updates
      |> Hooks.subscribe_to_posts()
      # Track page view
      |> Hooks.track_page_view("posts_index")
      # Assign current user from session
      |> assign_new(:current_user, fn ->
        if user_token = session["user_token"] do
          HypeArrow.Accounts.get_user_by_session_token(user_token)
        end
      end)

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    # Store the current expanded post ID before applying the action
    current_expanded_post_id = socket.assigns[:expanded_post_id]

    # Check if this is a direct URL access or from a click
    # If it's from a click, the from_direct_access flag will be false
    # If it's a direct URL access (typing URL or refreshing), we need to set the flag
    is_direct_access =
      socket.assigns.live_action == :show &&
        !socket.assigns[:from_direct_access] &&
        current_expanded_post_id == nil

    # Apply the action to update assigns based on the URL
    socket = apply_action(socket, socket.assigns.live_action, params)

    # If this is a direct URL access, set the flag and move the post to the top
    socket =
      if is_direct_access && socket.assigns.expanded_post_id do
        IO.puts("Direct URL access detected for post #{socket.assigns.expanded_post_id}")

        # Set the flag to indicate this is from direct URL access
        socket = assign(socket, :from_direct_access, true)

        # Move the post to the top of the list
        ensure_post_loaded(socket, socket.assigns.expanded_post_id)
      else
        # For clicks, we need to preserve the from_direct_access flag
        # This ensures we don't accidentally treat a click as direct access
        socket
      end

    {:noreply, socket}
  end

  # Ensure the specified post is loaded in the posts list and moved to the top
  # This is only used for direct URL access, not for clicking on posts
  defp ensure_post_loaded(socket, post_id) do
    # Check if the post is already in the loaded posts
    post_in_list? = Enum.any?(socket.assigns.posts, fn post -> post.id == post_id end)

    if post_in_list? do
      # Post is already in the list, move it to the top using PostUtils
      updated_posts = PostUtils.ensure_post_at_top(socket.assigns.posts, post_id)

      # Log that we're moving the post to the top
      IO.puts("Moving post #{post_id} to the top of the list for direct access")

      # Update the socket with the new posts list
      assign(socket, :posts, updated_posts)
    else
      # Post is not in the current list, fetch it and add it to the list
      case Social.get_post(post_id) do
        %HypeArrow.Social.Post{} = post ->
          # Remove any duplicates and add the post to the top
          filtered_posts = PostUtils.remove_post_from_list(socket.assigns.posts, post_id)
          updated_posts = [post | filtered_posts]

          # Log that we're adding the post to the list
          IO.puts("Adding post #{post_id} to the top of the list for direct access")

          # Update the socket with the new posts list
          assign(socket, :posts, updated_posts)

        nil ->
          # Post not found, don't modify the list
          socket
      end
    end
  end

  # Handle real-time post creation event
  @impl true
  def handle_info({:post_created, %{post: post}}, socket) do
    # Add the new post to the top of the list
    updated_posts = [post | socket.assigns.posts]

    # Ensure no duplicates
    deduplicated_posts = PostUtils.deduplicate_posts(updated_posts)

    {:noreply, assign(socket, :posts, deduplicated_posts)}
  end

  # Handle real-time post update event
  @impl true
  def handle_info({:post_updated, %{post: updated_post}}, socket) do
    # Update the post in the list using PostUtils
    updated_posts = PostUtils.update_post_in_list(socket.assigns.posts, updated_post)

    # If this post is currently being edited, clear the editing state
    # This prevents conflicts if another user updates the post while it's being edited
    socket =
      if socket.assigns.editing_post_id == updated_post.id do
        socket
        |> assign(:editing_post_id, nil)
        |> assign(:edit_form, nil)
        |> put_flash(:info, "This post was updated by another user")
      else
        socket
      end

    {:noreply, assign(socket, :posts, updated_posts)}
  end

  # Handle real-time post deletion event
  @impl true
  def handle_info({:post_deleted, %{post: deleted_post}}, socket) do
    # Remove the deleted post from the list using PostUtils
    updated_posts = PostUtils.remove_post_from_list(socket.assigns.posts, deleted_post.id)

    # If the deleted post was expanded or being edited, reset those states
    socket =
      cond do
        socket.assigns.expanded_post_id == deleted_post.id ->
          socket
          |> assign(:expanded_post_id, nil)
          |> assign(:editing_post_id, nil)
          |> assign(:edit_form, nil)
          |> put_flash(:info, "This post was deleted by another user")
          |> push_patch(to: ~p"/")

        socket.assigns.editing_post_id == deleted_post.id ->
          socket
          |> assign(:editing_post_id, nil)
          |> assign(:edit_form, nil)
          |> put_flash(:info, "This post was deleted by another user")

        true ->
          socket
      end

    {:noreply, assign(socket, :posts, updated_posts)}
  end

  defp apply_action(socket, :index, _params) do
    # Preserve the from_direct_access flag when collapsing
    from_direct_access = socket.assigns[:from_direct_access] || false

    socket
    |> assign(:page_title, "Feed")
    |> assign(:expanded_post_id, nil)
    |> assign(:from_direct_access, from_direct_access)
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    # Preserve the from_direct_access flag when expanding
    from_direct_access = socket.assigns[:from_direct_access] || false

    # Check if we're expanding for editing
    expanding_for_edit = socket.assigns[:expanding_for_edit] || false

    # If we're expanding for editing, preserve the editing state
    editing_post_id = if expanding_for_edit, do: socket.assigns[:editing_post_id], else: nil
    edit_form = if expanding_for_edit, do: socket.assigns[:edit_form], else: nil

    post_id = Helpers.safe_parse_integer(id)

    if post_id do
      post = Social.get_post(post_id)

      if post do
        socket =
          socket
          |> assign(:page_title, post.title)
          |> assign(:expanded_post_id, post.id)
          |> assign(:from_direct_access, from_direct_access)

        # Only clear editing state if we're not expanding for editing
        if expanding_for_edit do
          # Reset the expanding_for_edit flag but keep the editing state
          socket
          |> assign(:expanding_for_edit, false)
          |> assign(:editing_post_id, editing_post_id)
          |> assign(:edit_form, edit_form)
        else
          # Clear editing state as before
          socket
          |> assign(:editing_post_id, nil)
          |> assign(:edit_form, nil)
        end
      else
        # If post not found, redirect to index with error message
        ErrorUtils.handle_live_error(socket, :not_found, "Post not found")
      end
    else
      # Invalid ID format
      ErrorUtils.handle_live_error(socket, :bad_request, "Invalid post ID format")
    end
  end

  # Function to toggle post expansion
  def toggle_post(js \\ %JS{}, post_id) do
    js
    |> JS.push("toggle-post", value: %{id: post_id})
  end

  # Handle post toggle event
  @impl true
  def handle_event("toggle-post", %{"id" => post_id}, socket) do
    post_id = Helpers.to_integer(post_id)

    cond do
      # If this post is already expanded, collapse it
      socket.assigns.expanded_post_id == post_id ->
        # Check if this post was recently saved from edit mode
        was_recently_saved = socket.assigns[:recently_saved_post_id] == post_id

        # Always clear editing state when collapsing a post
        socket =
          socket
          |> assign(:expanded_post_id, nil)
          # Reset the flags
          |> assign(:from_direct_access, false)
          |> assign(:editing_post_id, nil)
          |> assign(:edit_form, nil)
          # Clear the recently saved flag
          |> assign(:recently_saved_post_id, nil)
          |> push_patch(to: ~p"/")

        # Push JavaScript to handle the post collapse first
        # Include information about whether this was recently saved to help with scroll handling
        socket = push_event(socket, "post-collapsed", %{recently_saved: was_recently_saved})

        # If this post was recently saved, restore the edit scroll position after a small delay
        # This ensures the post collapse visuals are handled first, then scroll is restored
        socket =
          if was_recently_saved do
            # Use a small delay to ensure post collapse completes before scroll restoration
            push_event(socket, "restore-edit-scroll-delayed", %{post_id: post_id})
          else
            socket
          end

        {:noreply, socket}

      # Otherwise, expand this post
      true ->
        # For clicks, we don't move the post to the top - we expand it in place
        # This preserves the original behavior for clicking

        # First, explicitly mark this as NOT from direct access
        # Do this before the push_patch to ensure the flag is set before handle_params is called
        socket = assign(socket, :from_direct_access, false)

        # Log that we're expanding a post from a click
        IO.puts("Expanding post #{post_id} from click (not direct access)")

        # Then update the expanded post ID and URL
        # Also clear any editing state to prevent issues when toggling posts
        socket =
          socket
          |> assign(:expanded_post_id, post_id)
          |> assign(:editing_post_id, nil)
          |> assign(:edit_form, nil)
          |> push_patch(to: ~p"/posts/#{post_id}")

        # Ensure no duplicates in the posts list using PostUtils
        current_posts = socket.assigns.posts
        deduplicated_posts = PostUtils.deduplicate_posts(current_posts)

        # If we found duplicates, update the posts list
        socket =
          if length(deduplicated_posts) < length(current_posts) do
            IO.puts(
              "Removed #{length(current_posts) - length(deduplicated_posts)} duplicate posts during toggle"
            )

            assign(socket, :posts, deduplicated_posts)
          else
            socket
          end

        # Push JavaScript to handle the post expansion
        # Add a flag to indicate this is from a click, not direct URL access
        {:noreply, push_event(socket, "post-expanded", %{id: post_id, from_click: true})}
    end
  end

  # Handle JavaScript events
  @impl true
  def handle_event("js-exec", %{"to" => _selector, "attr" => "data-post-expanded"}, socket) do
    {:noreply, socket}
  end

  # Handle ignore event (for buttons that should be handled by JS, not LiveView)
  @impl true
  def handle_event("ignore", _params, socket) do
    {:noreply, socket}
  end

  # Handle grid size change event
  @impl true
  def handle_event("change-grid-size", %{"size" => size}, socket) do
    # Convert size to integer using Helpers
    grid_size = Helpers.to_integer(size, 3)

    # Ensure grid size is between 1 and 4
    grid_size = max(1, min(4, grid_size))

    {:noreply, assign(socket, :grid_size, grid_size)}
  end

  # Handle opening the new post modal
  @impl true
  def handle_event("open-new-post-modal", _params, socket) do
    if socket.assigns.current_user do
      # Reset the form to a fresh state
      changeset = Social.change_post(%Post{})

      {:noreply,
       socket
       |> assign(:new_post_form, to_form(changeset))
       |> assign(:show_new_post_modal, true)}
    else
      {:noreply,
       socket
       |> put_flash(:error, "You must be logged in to create posts")
       |> push_navigate(to: ~p"/users/log_in")}
    end
  end

  # Handle closing the new post modal
  @impl true
  def handle_event("close-modal", _params, socket) do
    {:noreply, assign(socket, :show_new_post_modal, false)}
  end

  # Handle closing the edit post modal
  @impl true
  def handle_event("close-edit-modal", _params, socket) do
    # Get the post ID before clearing the edit_post
    post_id = if socket.assigns.edit_post, do: socket.assigns.edit_post.id, else: nil

    socket =
      socket
      |> assign(:show_edit_post_modal, false)
      |> assign(:edit_post, nil)

    # If we have a post ID, push event to restore scroll position
    if post_id do
      {:noreply, push_event(socket, "restore-edit-scroll", %{post_id: post_id})}
    else
      {:noreply, socket}
    end
  end

  # Handle new post form validation
  @impl true
  def handle_event("validate-new-post", %{"post" => post_params}, socket) do
    changeset =
      %Post{}
      |> Social.change_post(post_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :new_post_form, to_form(changeset))}
  end

  # Handle edit post form validation
  @impl true
  def handle_event("validate-edit-post", params, socket) do
    post_params = params["post"] || %{}
    post_id = socket.assigns.edit_post_id

    if post_id do
      post = Social.get_post(post_id)

      if post && socket.assigns.current_user && post.user_id == socket.assigns.current_user.id do
        changeset =
          post
          |> Social.change_post(post_params)
          |> Map.put(:action, :validate)

        {:noreply, assign(socket, :edit_post_form, to_form(changeset))}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  # Handle new post form submission
  @impl true
  def handle_event("save-new-post", %{"post" => post_params}, socket) do
    if socket.assigns.current_user do
      case Social.create_post(post_params, socket.assigns.current_user) do
        {:ok, post} ->
          # Update the posts list with the new post at the top
          updated_posts = [post | socket.assigns.posts]

          # Ensure no duplicates
          deduplicated_posts = PostUtils.deduplicate_posts(updated_posts)

          {:noreply,
           socket
           |> assign(:posts, deduplicated_posts)
           |> assign(:show_new_post_modal, false)
           |> put_flash(:info, "Post created successfully!")}

        {:error, changeset} ->
          {:noreply, assign(socket, :new_post_form, to_form(changeset))}
      end
    else
      {:noreply,
       socket
       |> put_flash(:error, "You must be logged in to create posts")
       |> assign(:show_new_post_modal, false)}
    end
  end

  # Handle edit post event (modal version - deprecated)
  @impl true
  def handle_event("edit-post", %{"id" => id}, socket) do
    post_id = Helpers.to_integer(id)

    if post_id do
      post = Social.get_post(post_id)

      if post && socket.assigns.current_user && post.user_id == socket.assigns.current_user.id do
        {:noreply,
         socket
         |> assign(:edit_post, post)
         |> assign(:show_edit_post_modal, true)}
      else
        {:noreply,
         socket
         |> put_flash(:error, "You are not authorized to edit this post")}
      end
    else
      {:noreply,
       socket
       |> put_flash(:error, "Invalid post ID")}
    end
  end

  # Handle enable edit mode event (called after post expansion is complete)
  @impl true
  def handle_event("enable-edit-mode", %{"id" => post_id}, socket) do
    IO.puts("🔧 ENABLE-EDIT-MODE EVENT RECEIVED for post_id: #{inspect(post_id)}")

    post_id = Helpers.to_integer(post_id)
    IO.puts("🔧 Parsed post_id: #{inspect(post_id)}")
    IO.puts("🔧 Current expanded_post_id: #{inspect(socket.assigns.expanded_post_id)}")
    IO.puts("🔧 Current editing_post_id: #{inspect(socket.assigns.editing_post_id)}")

    if post_id do
      post = Social.get_post(post_id)
      IO.puts("🔧 Post found: #{inspect(post != nil)}")
      IO.puts("🔧 Current user: #{inspect(socket.assigns.current_user != nil)}")
      IO.puts("🔧 Post user_id: #{inspect(post && post.user_id)}")

      IO.puts(
        "🔧 Current user_id: #{inspect(socket.assigns.current_user && socket.assigns.current_user.id)}"
      )

      if post && socket.assigns.current_user && post.user_id == socket.assigns.current_user.id do
        IO.puts("🔧 User authorized to edit post")

        # Ensure the post is expanded - if not, expand it first
        socket =
          if socket.assigns.expanded_post_id != post_id do
            IO.puts("🔧 Post not expanded, expanding it now")

            socket
            |> assign(:expanded_post_id, post_id)
            |> push_patch(to: ~p"/posts/#{post_id}")
          else
            IO.puts("🔧 Post already expanded")
            socket
          end

        # Create a form for editing
        changeset = Social.change_post(post)
        form = to_form(changeset)
        IO.puts("🔧 Created edit form: #{inspect(form != nil)}")

        # Set the editing state
        IO.puts("🔧 Setting editing_post_id to: #{post_id}")
        IO.puts("🔧 Setting edit_form")

        {:noreply,
         socket
         |> assign(:editing_post_id, post_id)
         |> assign(:edit_form, form)}
      else
        IO.puts("🔧 ERROR: User not authorized to edit post")

        {:noreply,
         socket
         |> put_flash(:error, "You are not authorized to edit this post")}
      end
    else
      IO.puts("🔧 ERROR: Invalid post ID")

      {:noreply,
       socket
       |> put_flash(:error, "Invalid post ID")}
    end
  end

  # Handle toggle edit post event (inline editing) - DEPRECATED
  # This is kept for backward compatibility but should not be used
  @impl true
  def handle_event("toggle-edit-post", %{"id" => id}, socket) do
    post_id = Helpers.to_integer(id)

    if post_id do
      post = Social.get_post(post_id)

      if post && socket.assigns.current_user && post.user_id == socket.assigns.current_user.id do
        # If the post is not expanded, expand it first
        socket =
          if socket.assigns.expanded_post_id != post_id do
            # Set the flag to indicate we're expanding for editing
            # This will be used in handle_params to preserve the editing state
            socket = assign(socket, :expanding_for_edit, true)

            # Create a form for editing now, before the route change
            changeset = Social.change_post(post)
            form = to_form(changeset)

            # Set the editing ID before expanding
            socket = assign(socket, :editing_post_id, post_id)
            socket = assign(socket, :edit_form, form)

            # Now expand the post with the editing state already set
            socket
            |> assign(:expanded_post_id, post_id)
            |> push_patch(to: ~p"/posts/#{post_id}")
          else
            socket
          end

        # If the post was already expanded, we need to create the form here
        # Otherwise, the form was already created before expanding
        if socket.assigns.expanded_post_id == post_id do
          # Create a form for editing
          changeset = Social.change_post(post)
          form = to_form(changeset)

          # Set the editing state and trigger scroll to the post
          {:noreply,
           socket
           |> assign(:editing_post_id, post_id)
           |> assign(:edit_form, form)
           |> push_event("edit-post-scroll", %{post_id: post_id})}
        else
          # The form was already created and the post is being expanded
          # Just return the socket as is, the scroll will happen after expansion
          {:noreply, socket |> push_event("edit-post-scroll", %{post_id: post_id})}
        end
      else
        {:noreply,
         socket
         |> put_flash(:error, "You are not authorized to edit this post")}
      end
    else
      {:noreply,
       socket
       |> put_flash(:error, "Invalid post ID")}
    end
  end

  # Handle expand and edit post event (single action for both expansion and edit mode)
  @impl true
  def handle_event("expand-and-edit-post", %{"id" => post_id}, socket) do
    post_id = Helpers.to_integer(post_id)

    if post_id do
      post = Social.get_post(post_id)

      if post && socket.assigns.current_user && post.user_id == socket.assigns.current_user.id do
        # Create a form for editing
        changeset = Social.change_post(post)
        form = to_form(changeset)

        # Set all the necessary state for expansion and editing
        # IMPORTANT: Set expanding_for_edit flag BEFORE push_patch so apply_action preserves our editing state
        socket =
          socket
          |> assign(:expanded_post_id, post_id)
          |> assign(:editing_post_id, post_id)
          |> assign(:edit_form, form)
          |> assign(:expanding_for_edit, true)
          |> assign(:from_direct_access, false)
          |> push_patch(to: ~p"/posts/#{post_id}")

        # Ensure no duplicates in the posts list using PostUtils
        current_posts = socket.assigns.posts
        deduplicated_posts = PostUtils.deduplicate_posts(current_posts)

        # If we found duplicates, update the posts list
        socket =
          if length(deduplicated_posts) < length(current_posts) do
            assign(socket, :posts, deduplicated_posts)
          else
            socket
          end

        # Push JavaScript to handle the post expansion and scroll to the post
        {:noreply,
         socket
         |> push_event("post-expanded", %{id: post_id, from_click: true})
         |> push_event("edit-post-scroll", %{post_id: post_id})}
      else
        {:noreply,
         socket
         |> put_flash(:error, "You are not authorized to edit this post")}
      end
    else
      {:noreply,
       socket
       |> put_flash(:error, "Invalid post ID")}
    end
  end

  # Handle cancel edit post event
  @impl true
  def handle_event("cancel-edit-post", %{"post_id" => _post_id}, socket) do
    # Clear the editing state but preserve expanded state
    # For cancel, we want to maintain the current viewport position and keep the post expanded
    expanded_post_id = socket.assigns.expanded_post_id

    socket =
      socket
      |> assign(:editing_post_id, nil)
      |> assign(:edit_form, nil)

    # If there's an expanded post, ensure JavaScript state is consistent
    if expanded_post_id do
      {:noreply, push_event(socket, "post-expanded", %{id: expanded_post_id, from_click: false})}
    else
      {:noreply, socket}
    end
  end

  # Fallback handler for cancel-edit-post with no post_id
  @impl true
  def handle_event("cancel-edit-post", _params, socket) do
    # Clear the editing state but preserve expanded state
    # For cancel, we want to maintain the current viewport position and keep the post expanded
    expanded_post_id = socket.assigns.expanded_post_id

    socket =
      socket
      |> assign(:editing_post_id, nil)
      |> assign(:edit_form, nil)

    # If there's an expanded post, ensure JavaScript state is consistent
    if expanded_post_id do
      {:noreply, push_event(socket, "post-expanded", %{id: expanded_post_id, from_click: false})}
    else
      {:noreply, socket}
    end
  end

  # Handle save edit post event (modal version)
  @impl true
  def handle_event("save-edit-post", params, socket) do
    post_params = params["post"] || %{}
    post_id = Helpers.to_integer(params["post_id"])
    post = socket.assigns.edit_post

    if post && post_id && post.id == post_id do
      case Social.update_post(post, post_params, socket.assigns.current_user) do
        {:ok, updated_post} ->
          # Update the post in the list
          updated_posts = PostUtils.update_post_in_list(socket.assigns.posts, updated_post)

          # After successful save, push event to restore scroll position
          {:noreply,
           socket
           |> assign(:posts, updated_posts)
           |> assign(:show_edit_post_modal, false)
           |> assign(:edit_post, nil)
           |> push_event("restore-edit-scroll", %{post_id: post_id})
           |> put_flash(:info, "Post updated successfully")}

        {:error, :unauthorized} ->
          {:noreply,
           socket
           |> assign(:show_edit_post_modal, false)
           |> assign(:edit_post, nil)
           |> put_flash(:error, "You are not authorized to update this post")}

        {:error, _changeset} ->
          {:noreply,
           socket
           |> put_flash(:error, "Failed to update post. Please check the form and try again.")}
      end
    else
      {:noreply,
       socket
       |> assign(:show_edit_post_modal, false)
       |> assign(:edit_post, nil)
       |> put_flash(:error, "Invalid post or post ID")}
    end
  end

  # Handle save inline edit post event
  @impl true
  def handle_event("save-inline-edit", params, socket) do
    post_params = params["post"] || %{}
    post_id = Helpers.to_integer(params["post_id"])

    if post_id && post_id == socket.assigns.editing_post_id do
      post = Social.get_post(post_id)

      if post && socket.assigns.current_user && post.user_id == socket.assigns.current_user.id do
        case Social.update_post(post, post_params, socket.assigns.current_user) do
          {:ok, updated_post} ->
            # Update the post in the list
            updated_posts = PostUtils.update_post_in_list(socket.assigns.posts, updated_post)

            # After successful save, clear editing state but PRESERVE expanded state
            # The post should remain expanded after saving to show the updated content
            {:noreply,
             socket
             |> assign(:posts, updated_posts)
             |> assign(:editing_post_id, nil)
             |> assign(:edit_form, nil)
             # Add a flag to indicate this post was just saved from edit mode
             |> assign(:recently_saved_post_id, post_id)
             # Keep expanded_post_id unchanged so post remains expanded
             # Push post-expanded event to ensure JavaScript state is consistent
             |> push_event("post-expanded", %{id: post_id, from_click: false})
             # NOTE: We don't restore scroll position here anymore - we'll do it when the post is closed
             # This prevents the scroll restoration from being overridden by subsequent post collapse events
             |> put_flash(:info, "Post updated successfully")}

          {:error, :unauthorized} ->
            {:noreply,
             socket
             |> assign(:editing_post_id, nil)
             |> assign(:edit_form, nil)
             |> put_flash(:error, "You are not authorized to update this post")}

          {:error, changeset} ->
            {:noreply,
             socket
             |> assign(:edit_form, to_form(changeset))
             |> put_flash(:error, "Failed to update post. Please check the form and try again.")}
        end
      else
        {:noreply,
         socket
         |> assign(:editing_post_id, nil)
         |> assign(:edit_form, nil)
         |> put_flash(:error, "You are not authorized to update this post")}
      end
    else
      {:noreply,
       socket
       |> assign(:editing_post_id, nil)
       |> assign(:edit_form, nil)
       |> put_flash(:error, "Invalid post ID")}
    end
  end

  # Handle delete post event
  @impl true
  def handle_event("delete-post", %{"id" => id}, socket) do
    post_id = Helpers.to_integer(id)

    if post_id do
      post = Social.get_post(post_id)

      if post && socket.assigns.current_user do
        case Social.delete_post(post, socket.assigns.current_user) do
          {:ok, _} ->
            # Remove the post from the list
            updated_posts = PostUtils.remove_post_from_list(socket.assigns.posts, post_id)

            # If the deleted post was expanded or being edited, reset those states
            socket =
              cond do
                socket.assigns.expanded_post_id == post_id ->
                  socket
                  |> assign(:expanded_post_id, nil)
                  |> assign(:editing_post_id, nil)
                  |> assign(:edit_form, nil)
                  |> push_patch(to: ~p"/")

                socket.assigns.editing_post_id == post_id ->
                  socket
                  |> assign(:editing_post_id, nil)
                  |> assign(:edit_form, nil)

                true ->
                  socket
              end

            {:noreply,
             socket
             |> assign(:posts, updated_posts)
             |> put_flash(:info, "Post deleted successfully")}

          {:error, :unauthorized} ->
            {:noreply,
             socket
             |> put_flash(:error, "You are not authorized to delete this post")}

          {:error, _} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to delete post")}
        end
      else
        {:noreply,
         socket
         |> put_flash(:error, "You are not authorized to delete this post")}
      end
    else
      {:noreply,
       socket
       |> put_flash(:error, "Invalid post ID")}
    end
  end

  # Handle infinite scroll load more event
  @impl true
  def handle_event("load-more-posts", _params, socket) do
    # Don't load more if already loading or no more posts
    if socket.assigns.loading or not socket.assigns.page_info.has_more do
      {:noreply, socket}
    else
      # Set loading state
      socket = assign(socket, loading: true)

      # Get the next cursor from the current page info
      next_cursor = socket.assigns.page_info.next_cursor

      # Load the next page of posts
      {new_posts, new_page_info} = Social.paginate_posts(next_cursor, @default_posts_per_page)

      # Merge the new posts with existing posts, avoiding duplicates
      merged_posts = PostUtils.merge_posts(socket.assigns.posts, new_posts)

      # Ensure no duplicates in the final list
      deduplicated_posts = PostUtils.deduplicate_posts(merged_posts)

      # Log if we filtered out any duplicates
      filtered_count = length(merged_posts) - length(deduplicated_posts)

      if filtered_count > 0 do
        IO.puts("Removed #{filtered_count} duplicate posts during load-more")
      end

      # Check if we actually got any new posts after filtering
      if length(deduplicated_posts) == length(socket.assigns.posts) and length(new_posts) > 0 do
        # If we got posts from the database but they were all filtered out as duplicates,
        # mark that there are no more posts to load
        updated_page_info = Map.put(new_page_info, :has_more, false)

        IO.puts("No new posts loaded after filtering, marking has_more as false")

        socket =
          socket
          |> assign(:posts, deduplicated_posts)
          |> assign(:page_info, updated_page_info)
          |> assign(:loading, false)

        {:noreply, socket}
      else
        # Either we got new posts, or there were no posts returned from the database
        # In either case, use the page_info from the database query
        socket =
          socket
          |> assign(:posts, deduplicated_posts)
          |> assign(:page_info, new_page_info)
          |> assign(:loading, false)

        # Debug logging
        IO.puts(
          "Updated posts list. has_more: #{new_page_info.has_more}, next_cursor: #{new_page_info.next_cursor || "nil"}"
        )

        {:noreply, socket}
      end
    end
  end
end
